import { create } from 'zustand'

export interface Question {
  id: number
  category: 'verbal' | 'quantitative' | 'nonverbal'
  difficulty: 'easy' | 'medium' | 'hard'
  question: string
  options: {
    A: string
    B: string
    C: string
    D: string
  }
  correctAnswer: 'A' | 'B' | 'C' | 'D'
  explanation: string
}

export interface TestData {
  testId: string
  generatedAt: string
  testType: 'timed' | 'untimed'
  totalQuestions: number
  timeLimit: number | null
  questions: Question[]
}

export interface UserAnswer {
  questionId: number
  selectedAnswer: 'A' | 'B' | 'C' | 'D' | null
  timeSpent: number
  isCorrect?: boolean
}

export interface TestState {
  // Test data
  testData: TestData | null
  userAnswers: UserAnswer[]
  currentQuestionIndex: number
  
  // Timer state
  timeRemaining: number | null
  isTimerActive: boolean
  startTime: number | null
  
  // Test status
  isTestStarted: boolean
  isTestCompleted: boolean
  testScore: number | null
  
  // Actions
  setTestData: (data: TestData) => void
  setUserAnswer: (questionId: number, answer: 'A' | 'B' | 'C' | 'D' | null) => void
  nextQuestion: () => void
  previousQuestion: () => void
  goToQuestion: (index: number) => void
  startTest: () => void
  completeTest: () => void
  startTimer: () => void
  stopTimer: () => void
  updateTimer: (timeRemaining: number) => void
  resetTest: () => void
  calculateScore: () => number
}

export const useTestStore = create<TestState>((set, get) => ({
  // Initial state
  testData: null,
  userAnswers: [],
  currentQuestionIndex: 0,
  timeRemaining: null,
  isTimerActive: false,
  startTime: null,
  isTestStarted: false,
  isTestCompleted: false,
  testScore: null,

  // Actions
  setTestData: (data: TestData) => {
    set({
      testData: data,
      timeRemaining: data.timeLimit,
      userAnswers: data.questions.map(q => ({
        questionId: q.id,
        selectedAnswer: null,
        timeSpent: 0,
        isCorrect: undefined
      }))
    })
  },

  setUserAnswer: (questionId: number, answer: 'A' | 'B' | 'C' | 'D' | null) => {
    set(state => ({
      userAnswers: state.userAnswers.map(ua =>
        ua.questionId === questionId
          ? { ...ua, selectedAnswer: answer }
          : ua
      )
    }))
  },

  nextQuestion: () => {
    set(state => {
      const nextIndex = Math.min(
        state.currentQuestionIndex + 1,
        (state.testData?.questions.length || 1) - 1
      )
      return { currentQuestionIndex: nextIndex }
    })
  },

  previousQuestion: () => {
    set(state => ({
      currentQuestionIndex: Math.max(state.currentQuestionIndex - 1, 0)
    }))
  },

  goToQuestion: (index: number) => {
    set(state => {
      const maxIndex = (state.testData?.questions.length || 1) - 1
      const validIndex = Math.max(0, Math.min(index, maxIndex))
      return { currentQuestionIndex: validIndex }
    })
  },

  startTest: () => {
    set({
      isTestStarted: true,
      startTime: Date.now()
    })
  },

  completeTest: () => {
    const state = get()
    const score = state.calculateScore()
    
    set({
      isTestCompleted: true,
      isTimerActive: false,
      testScore: score,
      userAnswers: state.userAnswers.map(ua => {
        const question = state.testData?.questions.find(q => q.id === ua.questionId)
        return {
          ...ua,
          isCorrect: question ? ua.selectedAnswer === question.correctAnswer : false
        }
      })
    })
  },

  startTimer: () => {
    set({ isTimerActive: true })
  },

  stopTimer: () => {
    set({ isTimerActive: false })
  },

  updateTimer: (timeRemaining: number) => {
    set({ timeRemaining })
    
    // Auto-complete test when time runs out
    if (timeRemaining <= 0) {
      get().completeTest()
    }
  },

  resetTest: () => {
    set({
      testData: null,
      userAnswers: [],
      currentQuestionIndex: 0,
      timeRemaining: null,
      isTimerActive: false,
      startTime: null,
      isTestStarted: false,
      isTestCompleted: false,
      testScore: null
    })
  },

  calculateScore: () => {
    const state = get()
    if (!state.testData) return 0
    
    const correctAnswers = state.userAnswers.filter(ua => {
      const question = state.testData!.questions.find(q => q.id === ua.questionId)
      return question && ua.selectedAnswer === question.correctAnswer
    }).length
    
    return Math.round((correctAnswers / state.testData.questions.length) * 100)
  }
}))
