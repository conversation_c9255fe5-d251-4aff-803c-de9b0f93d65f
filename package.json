{"name": "cogatv1", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-toast": "^1.2.15", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.541.0", "next": "15.5.0", "openai": "^5.15.0", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.0", "tailwindcss": "^4", "typescript": "^5"}}