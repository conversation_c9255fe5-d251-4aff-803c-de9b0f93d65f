# COGAT Practice Test App

A Next.js application for practicing COGAT (Cognitive Abilities Test) questions with AI-generated content, timer functionality, and detailed explanations.

## Features

- **AI-Generated Questions**: Fresh COGAT questions generated using OpenAI's GPT-4
- **Timed & Untimed Modes**: Practice under real test conditions or at your own pace
- **Three Question Categories**: Verbal, Quantitative, and Nonverbal reasoning
- **Real-time Timer**: Visual countdown for timed tests with auto-submission
- **Question Navigation**: Jump between questions and track progress
- **Detailed Results**: Comprehensive score breakdown with explanations
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- OpenAI API key

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd cogatv1
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```bash
cp .env.example .env.local
```

4. Add your OpenAI API key to `.env.local`:

```
OPENAI_API_KEY=your_openai_api_key_here
```

5. Run the development server:

```bash
npm run dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Usage

1. **Start**: Click "Start Practice Test" on the homepage
2. **Choose Mode**: Select timed (20 minutes) or untimed practice
3. **Take Test**: Answer questions one at a time with intuitive navigation
4. **Review Results**: See your score and detailed explanations for each question

## Project Structure

```
app/
├── api/generate-test/     # OpenAI integration for test generation
├── test/
│   ├── setup/            # Test configuration page
│   ├── take/             # Test taking interface
│   └── results/          # Results and review page
├── globals.css           # Global styles
├── layout.tsx            # Root layout
└── page.tsx              # Homepage with hero section

lib/
├── store.ts              # Zustand state management
└── utils.ts              # Utility functions

components/               # Reusable UI components (future)
```

## Technologies Used

- **Next.js 15**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first styling
- **Zustand**: Lightweight state management
- **OpenAI API**: AI-powered question generation
- **Lucide React**: Beautiful icons

## API Integration

The app integrates with OpenAI's GPT-4 to generate authentic COGAT questions. The API route (`/api/generate-test`) creates questions covering:

- Verbal reasoning (analogies, sentence completion)
- Quantitative reasoning (number patterns, math puzzles)
- Nonverbal reasoning (figure matrices, pattern recognition)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For questions or issues, please open a GitHub issue or contact the development team.
