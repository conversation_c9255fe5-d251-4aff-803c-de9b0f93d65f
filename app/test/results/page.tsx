"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  Brain,
  CheckCircle,
  XCircle,
  RotateCcw,
  Home,
  Trophy,
  Target,
} from "lucide-react";
import { useTestStore } from "@/lib/store";
import Link from "next/link";

export default function TestResults() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  const { testData, userAnswers, testScore, isTestCompleted, resetTest } =
    useTestStore();

  useEffect(() => {
    if (!isTestCompleted || !testData || testScore === null) {
      router.push("/test/setup");
    } else {
      setIsLoading(false);
    }
  }, [isTestCompleted, testData, testScore, router]);

  const handleNewTest = () => {
    resetTest();
    sessionStorage.removeItem("currentTest");
    router.push("/test/setup");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading results...</p>
        </div>
      </div>
    );
  }

  if (!testData || testScore === null) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">No test results found</p>
          <Link
            href="/test/setup"
            className="bg-blue-600 text-white px-6 py-2 rounded-lg"
          >
            Start New Test
          </Link>
        </div>
      </div>
    );
  }

  const correctAnswers = userAnswers.filter((ua) => ua.isCorrect).length;
  const totalQuestions = testData.questions.length;

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreMessage = (score: number) => {
    if (score >= 90)
      return "Excellent work! You have a strong understanding of COGAT concepts.";
    if (score >= 80)
      return "Great job! You're well-prepared for the COGAT test.";
    if (score >= 70)
      return "Good effort! Review the explanations to improve further.";
    if (score >= 60)
      return "Keep practicing! Focus on the areas where you missed questions.";
    return "Don't give up! More practice will help you improve significantly.";
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Brain className="h-6 w-6 text-blue-600" />
              <h1 className="text-xl font-bold text-gray-900">Test Results</h1>
            </div>

            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors"
              >
                <Home className="h-4 w-4" />
                <span>Home</span>
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Score Summary */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 mb-8 text-center">
            <div className="mb-6">
              <Trophy
                className={`h-16 w-16 mx-auto mb-4 ${getScoreColor(testScore)}`}
              />
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Test Complete!
              </h2>
              <p className="text-gray-600">{getScoreMessage(testScore)}</p>
            </div>

            <div className="grid md:grid-cols-3 gap-6 mb-6">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center justify-center mb-2">
                  <Target className="h-6 w-6 text-blue-600 mr-2" />
                  <span className="font-semibold text-gray-900">Score</span>
                </div>
                <div
                  className={`text-3xl font-bold ${getScoreColor(testScore)}`}
                >
                  {testScore}%
                </div>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center justify-center mb-2">
                  <CheckCircle className="h-6 w-6 text-green-600 mr-2" />
                  <span className="font-semibold text-gray-900">Correct</span>
                </div>
                <div className="text-3xl font-bold text-green-600">
                  {correctAnswers}
                </div>
              </div>

              <div className="bg-red-50 p-4 rounded-lg">
                <div className="flex items-center justify-center mb-2">
                  <XCircle className="h-6 w-6 text-red-600 mr-2" />
                  <span className="font-semibold text-gray-900">Incorrect</span>
                </div>
                <div className="text-3xl font-bold text-red-600">
                  {totalQuestions - correctAnswers}
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={handleNewTest}
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium flex items-center justify-center space-x-2"
              >
                <RotateCcw className="h-4 w-4" />
                <span>Take Another Test</span>
              </button>

              <Link
                href="/"
                className="border-2 border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-lg font-medium flex items-center justify-center space-x-2"
              >
                <Home className="h-4 w-4" />
                <span>Back to Home</span>
              </Link>
            </div>
          </div>

          {/* Question Review */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">
              Question Review
            </h3>

            <div className="space-y-8">
              {testData.questions.map((question, index) => {
                const userAnswer = userAnswers.find(
                  (ua) => ua.questionId === question.id
                );
                const isCorrect = userAnswer?.isCorrect || false;

                return (
                  <div
                    key={question.id}
                    className="border-b border-gray-200 pb-8 last:border-b-0"
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <span className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm font-medium">
                          Question {index + 1}
                        </span>
                        <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium capitalize">
                          {question.category}
                        </span>
                        {isCorrect ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-600" />
                        )}
                      </div>
                    </div>

                    <h4 className="text-lg font-semibold text-gray-900 mb-4">
                      {question.question}
                    </h4>

                    <div className="grid md:grid-cols-2 gap-4 mb-4">
                      {Object.entries(question.options).map(([key, value]) => {
                        const isUserAnswer = userAnswer?.selectedAnswer === key;
                        const isCorrectAnswer = question.correctAnswer === key;

                        let bgColor = "bg-gray-50";
                        let textColor = "text-gray-900";
                        let borderColor = "border-gray-200";

                        if (isCorrectAnswer) {
                          bgColor = "bg-green-50";
                          textColor = "text-green-900";
                          borderColor = "border-green-300";
                        } else if (isUserAnswer && !isCorrect) {
                          bgColor = "bg-red-50";
                          textColor = "text-red-900";
                          borderColor = "border-red-300";
                        }

                        return (
                          <div
                            key={key}
                            className={`p-3 rounded-lg border-2 ${bgColor} ${borderColor}`}
                          >
                            <div className="flex items-center">
                              <span
                                className={`w-6 h-6 rounded-full flex items-center justify-center text-sm font-semibold mr-3 ${
                                  isCorrectAnswer
                                    ? "bg-green-600 text-white"
                                    : isUserAnswer && !isCorrect
                                    ? "bg-red-600 text-white"
                                    : "bg-gray-300 text-gray-600"
                                }`}
                              >
                                {key}
                              </span>
                              <span className={textColor}>{value}</span>
                              {isUserAnswer && (
                                <span className="ml-auto text-sm font-medium">
                                  Your answer
                                </span>
                              )}
                              {isCorrectAnswer && (
                                <span className="ml-auto text-sm font-medium text-green-600">
                                  Correct
                                </span>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>

                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h5 className="font-semibold text-blue-900 mb-2">
                        Explanation:
                      </h5>
                      <p className="text-blue-800">{question.explanation}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
