"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  Brain,
  Clock,
  ChevronLeft,
  ChevronRight,
  Flag,
  X,
  AlertTriangle,
} from "lucide-react";
import { useTestStore } from "@/lib/store";
import { formatTime } from "@/lib/utils";

export default function TakeTest() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [showExitDialog, setShowExitDialog] = useState(false);

  const {
    testData,
    userAnswers,
    currentQuestionIndex,
    timeRemaining,
    isTimerActive,
    isTestCompleted,
    setTestData,
    setUserAnswer,
    nextQuestion,
    previousQuestion,
    goToQuestion,
    startTest,
    completeTest,
    startTimer,
    updateTimer,
  } = useTestStore();

  // Load test data from sessionStorage on mount
  useEffect(() => {
    const storedTest = sessionStorage.getItem("currentTest");
    if (storedTest) {
      try {
        const testInfo = JSON.parse(storedTest);
        setTestData(testInfo);
        startTest();

        if (testInfo.testType === "timed") {
          startTimer();
        }
      } catch (error) {
        console.error("Error loading test data:", error);
        router.push("/test/setup");
      }
    } else {
      router.push("/test/setup");
    }
    setIsLoading(false);
  }, [setTestData, startTest, startTimer, router]);

  // Timer effect
  useEffect(() => {
    if (!isTimerActive || timeRemaining === null || timeRemaining <= 0) return;

    const interval = setInterval(() => {
      updateTimer(timeRemaining - 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [isTimerActive, timeRemaining, updateTimer]);

  // Redirect to results when test is completed
  useEffect(() => {
    if (isTestCompleted) {
      router.push("/test/results");
    }
  }, [isTestCompleted, router]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === "Escape" && !showExitDialog) {
        handleExitTest();
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [showExitDialog]);

  const handleAnswerSelect = (answer: "A" | "B" | "C" | "D") => {
    if (!testData) return;
    const currentQuestion = testData.questions[currentQuestionIndex];
    setUserAnswer(currentQuestion.id, answer);
  };

  const handleCompleteTest = () => {
    completeTest();
  };

  const handleExitTest = () => {
    setShowExitDialog(true);
  };

  const confirmExit = () => {
    // Clear test data and navigate away
    sessionStorage.removeItem("currentTest");
    router.push("/test/setup");
  };

  const cancelExit = () => {
    setShowExitDialog(false);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your test...</p>
        </div>
      </div>
    );
  }

  if (!testData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">No test data found</p>
          <button
            onClick={() => router.push("/test/setup")}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg"
          >
            Start New Test
          </button>
        </div>
      </div>
    );
  }

  const currentQuestion = testData.questions[currentQuestionIndex];
  const currentAnswer = userAnswers.find(
    (ua) => ua.questionId === currentQuestion.id
  );
  const answeredCount = userAnswers.filter(
    (ua) => ua.selectedAnswer !== null
  ).length;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Brain className="h-6 w-6 text-blue-600" />
              <h1 className="text-xl font-bold text-gray-900">
                COGAT Practice Test
              </h1>
            </div>

            <div className="flex items-center space-x-6">
              {/* Timer */}
              {testData.testType === "timed" && timeRemaining !== null && (
                <div className="flex items-center space-x-2">
                  <Clock className="h-5 w-5 text-gray-600" />
                  <span
                    className={`font-mono text-lg ${
                      timeRemaining < 300 ? "text-red-600" : "text-gray-900"
                    }`}
                  >
                    {formatTime(timeRemaining)}
                  </span>
                </div>
              )}

              {/* Progress */}
              <div className="text-sm text-gray-600">
                Question {currentQuestionIndex + 1} of{" "}
                {testData.questions.length}
              </div>

              {/* Exit Button */}
              <button
                onClick={handleExitTest}
                className="flex items-center space-x-2 px-4 py-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
                title="Exit Test (ESC)"
              >
                <X className="h-4 w-4" />
                <span className="hidden sm:inline">Exit</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progress</span>
              <span>
                {answeredCount} of {testData.questions.length} answered
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${
                    (answeredCount / testData.questions.length) * 100
                  }%`,
                }}
              ></div>
            </div>
          </div>

          {/* Question Card */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 mb-8">
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium capitalize">
                  {currentQuestion.category} • {currentQuestion.difficulty}
                </span>
                <span className="text-gray-500 text-sm">
                  Question {currentQuestionIndex + 1}
                </span>
              </div>

              <h2 className="text-xl font-semibold text-gray-900 leading-relaxed">
                {currentQuestion.question}
              </h2>
            </div>

            {/* Answer Options */}
            <div className="space-y-3">
              {Object.entries(currentQuestion.options).map(([key, value]) => (
                <button
                  key={key}
                  onClick={() =>
                    handleAnswerSelect(key as "A" | "B" | "C" | "D")
                  }
                  className={`w-full text-left p-4 rounded-lg border-2 transition-all ${
                    currentAnswer?.selectedAnswer === key
                      ? "border-blue-600 bg-blue-50"
                      : "border-gray-200 hover:border-blue-300 hover:bg-gray-50"
                  }`}
                >
                  <div className="flex items-center">
                    <span
                      className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold mr-3 ${
                        currentAnswer?.selectedAnswer === key
                          ? "bg-blue-600 text-white"
                          : "bg-gray-200 text-gray-600"
                      }`}
                    >
                      {key}
                    </span>
                    <span className="text-gray-900">{value}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between">
            <button
              onClick={previousQuestion}
              disabled={currentQuestionIndex === 0}
              className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors ${
                currentQuestionIndex === 0
                  ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
              }`}
            >
              <ChevronLeft className="h-4 w-4" />
              <span>Previous</span>
            </button>

            <div className="flex items-center space-x-4">
              {currentQuestionIndex === testData.questions.length - 1 ? (
                <button
                  onClick={handleCompleteTest}
                  className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-medium flex items-center space-x-2"
                >
                  <Flag className="h-4 w-4" />
                  <span>Complete Test</span>
                </button>
              ) : (
                <button
                  onClick={nextQuestion}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium flex items-center space-x-2"
                >
                  <span>Next</span>
                  <ChevronRight className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>

          {/* Question Navigator */}
          <div className="mt-8 bg-white rounded-xl p-6 border border-gray-200">
            <h3 className="font-semibold text-gray-900 mb-4">
              Question Navigator
            </h3>
            <div className="grid grid-cols-5 sm:grid-cols-10 gap-2">
              {testData.questions.map((_, index) => {
                const isAnswered = userAnswers[index]?.selectedAnswer !== null;
                const isCurrent = index === currentQuestionIndex;

                return (
                  <button
                    key={index}
                    onClick={() => goToQuestion(index)}
                    className={`w-10 h-10 rounded-lg text-sm font-medium transition-colors ${
                      isCurrent
                        ? "bg-blue-600 text-white"
                        : isAnswered
                        ? "bg-green-100 text-green-800 border border-green-300"
                        : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                    }`}
                  >
                    {index + 1}
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </main>

      {/* Exit Confirmation Dialog */}
      {showExitDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md mx-4 shadow-xl">
            <div className="flex items-center space-x-3 mb-4">
              <div className="bg-red-100 p-2 rounded-full">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                Exit Test?
              </h3>
            </div>

            <p className="text-gray-600 mb-6">
              Are you sure you want to exit the test? Your progress will be lost
              and you&apos;ll need to start over.
            </p>

            <div className="flex space-x-3">
              <button
                onClick={cancelExit}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Continue Test
              </button>
              <button
                onClick={confirmExit}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Exit Test
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
