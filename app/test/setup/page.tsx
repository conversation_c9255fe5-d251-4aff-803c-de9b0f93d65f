"use client";

import { useState } from "react";
import { <PERSON>, <PERSON>, <PERSON>, ArrowR<PERSON>, ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function TestSetup() {
  const [testType, setTestType] = useState<"timed" | "untimed" | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const router = useRouter();

  const handleStartTest = async () => {
    if (!testType) return;

    setIsGenerating(true);

    try {
      // Generate test questions
      const response = await fetch("/api/generate-test", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          testType,
          questionCount: 10,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to generate test");
      }

      const testData = await response.json();

      // Store test data in sessionStorage
      sessionStorage.setItem(
        "currentTest",
        JSON.stringify({
          ...testData,
          testType,
          startTime: Date.now(),
          userAnswers: [],
          currentQuestion: 0,
        })
      );

      // Navigate to test page
      router.push("/test/take");
    } catch (error) {
      console.error("Error generating test:", error);
      alert("Failed to generate test. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <Brain className="h-8 w-8 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">COGAT Practice</h1>
          </Link>
          <Link
            href="/"
            className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Home
          </Link>
        </div>
      </header>

      <main className="container mx-auto px-4 py-12">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Choose Your Test Mode
            </h2>
            <p className="text-lg text-gray-600">
              Select whether you&apos;d like to practice with or without time
              pressure.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6 mb-8">
            {/* Timed Test Option */}
            <div
              className={`bg-white p-8 rounded-xl border-2 cursor-pointer transition-all ${
                testType === "timed"
                  ? "border-blue-600 bg-blue-50"
                  : "border-gray-200 hover:border-blue-300"
              }`}
              onClick={() => setTestType("timed")}
            >
              <div className="text-center">
                <div
                  className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
                    testType === "timed" ? "bg-blue-600" : "bg-blue-100"
                  }`}
                >
                  <Clock
                    className={`h-8 w-8 ${
                      testType === "timed" ? "text-white" : "text-blue-600"
                    }`}
                  />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Timed Test
                </h3>
                <p className="text-gray-600 mb-4">
                  Practice under real test conditions with a 20-minute timer.
                </p>
                <div className="text-sm text-gray-500">
                  • 10 questions • 20 minutes total • Automatic submission when
                  time expires
                </div>
              </div>
            </div>

            {/* Untimed Test Option */}
            <div
              className={`bg-white p-8 rounded-xl border-2 cursor-pointer transition-all ${
                testType === "untimed"
                  ? "border-green-600 bg-green-50"
                  : "border-gray-200 hover:border-green-300"
              }`}
              onClick={() => setTestType("untimed")}
            >
              <div className="text-center">
                <div
                  className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
                    testType === "untimed" ? "bg-green-600" : "bg-green-100"
                  }`}
                >
                  <Infinity
                    className={`h-8 w-8 ${
                      testType === "untimed" ? "text-white" : "text-green-600"
                    }`}
                  />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Untimed Test
                </h3>
                <p className="text-gray-600 mb-4">
                  Take your time to learn and understand each question.
                </p>
                <div className="text-sm text-gray-500">
                  • 10 questions • No time limit • Focus on learning
                </div>
              </div>
            </div>
          </div>

          {/* Start Test Button */}
          <div className="text-center">
            <button
              onClick={handleStartTest}
              disabled={!testType || isGenerating}
              className={`px-8 py-4 rounded-lg font-semibold text-lg transition-all flex items-center justify-center gap-2 mx-auto ${
                testType && !isGenerating
                  ? "bg-blue-600 hover:bg-blue-700 text-white"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  Generating Test...
                </>
              ) : (
                <>
                  Start Test
                  <ArrowRight className="h-5 w-5" />
                </>
              )}
            </button>

            {!testType && (
              <p className="text-sm text-gray-500 mt-2">
                Please select a test mode to continue
              </p>
            )}
          </div>

          {/* Test Information */}
          <div className="mt-12 bg-white p-6 rounded-xl border border-gray-200">
            <h4 className="font-semibold text-gray-900 mb-3">
              What to Expect:
            </h4>
            <ul className="space-y-2 text-gray-600">
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                10 AI-generated COGAT questions covering verbal, quantitative,
                and nonverbal reasoning
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                Questions will be presented one at a time
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                You can navigate between questions during the test
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                Detailed explanations will be provided after completion
              </li>
            </ul>
          </div>
        </div>
      </main>
    </div>
  );
}
