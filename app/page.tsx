"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>r, <PERSON><PERSON><PERSON> } from "lucide-react";
import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Brain className="h-8 w-8 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">COGAT Practice</h1>
          </div>
          <nav className="hidden md:flex space-x-6">
            <a
              href="#about"
              className="text-gray-600 hover:text-blue-600 transition-colors"
            >
              About
            </a>
            <a
              href="#how-it-works"
              className="text-gray-600 hover:text-blue-600 transition-colors"
            >
              How It Works
            </a>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-4 py-12">
        <div className="text-center max-w-4xl mx-auto">
          <h2 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            Master the <span className="text-blue-600">COGAT</span> Test
          </h2>
          <p className="text-xl text-gray-600 mb-8 leading-relaxed">
            Practice with AI-generated COGAT questions designed to help you
            excel. Get instant feedback, detailed explanations, and track your
            progress.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link
              href="/test/setup"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors flex items-center justify-center gap-2"
            >
              Start Practice Test
              <ArrowRight className="h-5 w-5" />
            </Link>
            <button className="border-2 border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-lg font-semibold text-lg transition-colors">
              Learn More
            </button>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <div className="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <Brain className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              AI-Generated Questions
            </h3>
            <p className="text-gray-600">
              Get fresh, challenging questions generated by advanced AI that
              mirror real COGAT test patterns.
            </p>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <div className="bg-green-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <Timer className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Timed & Untimed Options
            </h3>
            <p className="text-gray-600">
              Practice under real test conditions with timers, or take your time
              to learn at your own pace.
            </p>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <div className="bg-purple-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <BookOpen className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Detailed Explanations
            </h3>
            <p className="text-gray-600">
              Understand your mistakes with comprehensive explanations for every
              question and answer choice.
            </p>
          </div>
        </div>

        {/* How It Works Section */}
        <section
          id="how-it-works"
          className="bg-white rounded-2xl p-8 md:p-12 shadow-sm border border-gray-100"
        >
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-12">
            How It Works
          </h3>

          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="bg-blue-600 text-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                1
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">
                Choose Test Type
              </h4>
              <p className="text-gray-600 text-sm">
                Select timed or untimed practice mode based on your preference.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-blue-600 text-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                2
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">
                AI Generates Test
              </h4>
              <p className="text-gray-600 text-sm">
                Our AI creates a custom COGAT test with questions and answers
                instantly.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-blue-600 text-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                3
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">
                Take the Test
              </h4>
              <p className="text-gray-600 text-sm">
                Answer questions one at a time with an intuitive interface.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-blue-600 text-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                4
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">
                Review Results
              </h4>
              <p className="text-gray-600 text-sm">
                Get your score and detailed explanations for every question.
              </p>
            </div>
          </div>
        </section>

        {/* About COGAT Section */}
        <section id="about" className="mt-16 text-center max-w-3xl mx-auto">
          <h3 className="text-3xl font-bold text-gray-900 mb-6">
            About the COGAT Test
          </h3>
          <p className="text-lg text-gray-600 leading-relaxed mb-6">
            The Cognitive Abilities Test (COGAT) measures students&apos; learned
            reasoning abilities in three areas: verbal, quantitative, and
            nonverbal. Our practice tests help you prepare for all three
            sections with realistic questions and comprehensive explanations.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <span className="bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium">
              Verbal Reasoning
            </span>
            <span className="bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-medium">
              Quantitative Reasoning
            </span>
            <span className="bg-purple-100 text-purple-800 px-4 py-2 rounded-full text-sm font-medium">
              Nonverbal Reasoning
            </span>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-50 mt-20">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-gray-600">
            <p>&copy; 2024 COGAT Practice. Built with Next.js and AI.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
