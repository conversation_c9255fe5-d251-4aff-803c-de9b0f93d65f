import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON><PERSON> from "openai";

function getFallbackTest(testType: string) {
  return {
    testId: `fallback_${Date.now()}`,
    generatedAt: new Date().toISOString(),
    testType,
    totalQuestions: 3,
    timeLimit: testType === "timed" ? 20 * 60 : null,
    questions: [
      {
        id: 1,
        category: "verbal",
        difficulty: "medium",
        question: "Complete the analogy: Cat is to Kit<PERSON> as Dog is to ____",
        options: {
          A: "Bark",
          B: "Puppy",
          C: "Bone",
          D: "Tail",
        },
        correctAnswer: "B",
        explanation:
          "A kitten is a young cat, so a puppy is a young dog. This is an analogy based on the relationship between adult animals and their offspring.",
      },
      {
        id: 2,
        category: "quantitative",
        difficulty: "medium",
        question: "What number comes next in this sequence: 2, 4, 8, 16, ____",
        options: {
          A: "24",
          B: "32",
          C: "20",
          D: "18",
        },
        correctAnswer: "B",
        explanation:
          "Each number is doubled to get the next number: 2×2=4, 4×2=8, 8×2=16, 16×2=32.",
      },
      {
        id: 3,
        category: "nonverbal",
        difficulty: "easy",
        question:
          "Which shape completes the pattern? [Circle, Square, Circle, Square, ____]",
        options: {
          A: "Triangle",
          B: "Circle",
          C: "Square",
          D: "Rectangle",
        },
        correctAnswer: "B",
        explanation:
          "The pattern alternates between Circle and Square, so the next shape should be Circle.",
      },
    ],
  };
}

export async function POST(request: NextRequest) {
  const { testType, questionCount = 10 } = await request.json();

  try {
    // Check if OpenAI API key is available
    if (!process.env.OPENAI_API_KEY) {
      console.warn("OpenAI API key not found, using fallback questions");
      return NextResponse.json(getFallbackTest(testType));
    }

    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const prompt = `Generate ${questionCount} COGAT (Cognitive Abilities Test) practice questions. 
    
    The questions should cover three main areas:
    1. Verbal Reasoning (analogies, sentence completion, verbal classification)
    2. Quantitative Reasoning (number analogies, number puzzles, number series)
    3. Nonverbal Reasoning (figure matrices, figure classification, paper folding)
    
    For each question, provide:
    - A clear question statement
    - 4 multiple choice options (A, B, C, D)
    - The correct answer
    - A detailed explanation of why the answer is correct
    - The reasoning category (verbal, quantitative, or nonverbal)
    - Difficulty level (easy, medium, hard)
    
    Return the response as a JSON object with this exact structure:
    {
      "questions": [
        {
          "id": 1,
          "category": "verbal|quantitative|nonverbal",
          "difficulty": "easy|medium|hard",
          "question": "Question text here",
          "options": {
            "A": "Option A text",
            "B": "Option B text", 
            "C": "Option C text",
            "D": "Option D text"
          },
          "correctAnswer": "A|B|C|D",
          "explanation": "Detailed explanation of the correct answer and reasoning"
        }
      ]
    }
    
    Make sure the questions are age-appropriate for elementary to middle school students and follow authentic COGAT question patterns.`;

    const completion = await openai.chat.completions.create({
      model: "gpt-5",
      messages: [
        {
          role: "system",
          content:
            "You are an expert educational content creator specializing in COGAT test preparation. Generate high-quality, authentic COGAT practice questions.",
        },
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature: 0.7,
      max_tokens: 3000,
    });

    const responseText = completion.choices[0].message.content;

    if (!responseText) {
      throw new Error("No response from OpenAI");
    }

    // Parse the JSON response
    let testData;
    try {
      testData = JSON.parse(responseText);
    } catch {
      console.error("Failed to parse OpenAI response:", responseText);
      throw new Error("Invalid response format from AI");
    }

    // Validate the response structure
    if (!testData.questions || !Array.isArray(testData.questions)) {
      throw new Error("Invalid test data structure");
    }

    // Add metadata
    const enrichedTestData = {
      ...testData,
      testId: `test_${Date.now()}`,
      generatedAt: new Date().toISOString(),
      testType,
      totalQuestions: testData.questions.length,
      timeLimit: testType === "timed" ? 20 * 60 : null, // 20 minutes in seconds
    };

    return NextResponse.json(enrichedTestData);
  } catch (error) {
    console.error("Error generating test:", error);
    return NextResponse.json(getFallbackTest(testType));
  }
}
